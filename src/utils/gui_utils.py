# 可视化操作工具类
import binascii
import logging
import os
import sys
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Union, Optional

from PIL import Image

from config import gui_constants
from utils import server_utils

logger = logging.getLogger(__name__)

# 将指定窗口居中
def center_window(cur_gui:Union[tk.Tk,tk.Toplevel]):
    logger.info("centering window")
    cur_gui.update_idletasks()  # 更新所有挂起的GUI任务

    cur_gui_width = cur_gui.winfo_width() or gui_constants.DEFAULT_GUI_WIDTH
    cur_gui_height = cur_gui.winfo_height() or gui_constants.DEFAULT_GUI_HEIGHT
    logger.info(f"window dimensions: {cur_gui_width}x{cur_gui_height}")

    # 获取屏幕尺寸
    screen_width = cur_gui.winfo_screenwidth()
    screen_height = cur_gui.winfo_screenheight()
    logger.info(f"screen dimensions: {screen_width}x{screen_height}")

    x = (screen_width - cur_gui_width )// 2
    y = (screen_height - cur_gui_height )// 2
    logger.info(f"calculated center position: x={x}, y={y}")

    cur_gui.geometry(f"+{x}+{y}")# 设置窗口居中
    logger.info("window geometry set to center position")

    # 确保窗口在屏幕边界内
    cur_gui.update_idletasks()
    if x < 0 or y < 0 or (x + cur_gui_width) > screen_width or (y + cur_gui_height) > screen_height:
        logger.warning("window position exceeds screen boundaries, adjusting")
        # 如果窗口超出屏幕边界，重新调整到可见位置
        x = max(0, min(x, screen_width - cur_gui_width))
        y = max(0, min(y, screen_height - cur_gui_height))
        logger.info(f"adjusted position: x={x}, y={y}")
        cur_gui.geometry(f"+{x}+{y}")

    logger.info("window centering completed")

# 在可视化程序中如果存在程序问题,直接弹出错误信息框,然后关闭程序
def handle_code_error(error:BaseException|None, error_msg:str,no_exit:bool=True):
    """集中处理代码错误:直接停止程序"""
    if error:
        logger.exception(f"{error_msg}: {error}")
    else:
        logger.error(error_msg)
    need_exit = not no_exit
    if need_exit:
        error_msg=f"{error_msg}\n\n请联系管理员处理"
    messagebox.showerror("程序错误",error_msg)
    if need_exit:
      sys.exit(1)

# 右键显示菜单
def right_click_show_menu(cur_tree:ttk.Treeview, cur_menu:tk.Menu):
    def right_click_show_menu_handler(event: tk.Event):
        """处理Treeview右键点击事件，显示上下文菜单"""
        # 获取点击位置的行
        row_id = cur_tree.identify_row(event.y)
        if row_id:
            # 选中右键点击的行
            cur_tree.selection_set(row_id)
            # 在鼠标位置显示菜单
            cur_menu.post(event.x_root, event.y_root)
    cur_tree.bind("<Button-3>", func=right_click_show_menu_handler)

# 双击事件处理函数
def on_tree_double_click(cur_tree:ttk.Treeview,funct:callable):
    def double_click_handler(event: tk.Event):
        """处理Treeview双击事件，触发指定函数操作"""
        region = cur_tree.identify("region", event.x, event.y)
        # 只响应在单元格上的双击（排除表头等其他区域）
        if region == "cell":
            server_utils.logger_print(msg=f"double click detected on treeview row: {cur_tree.item(cur_tree.focus())}", custom_logger=logger,log_level=logging.DEBUG)
            funct()
    cur_tree.bind("<Double-1>",func=double_click_handler)


def is_png(file_png_path, strict_check=True)->bool:
    """
    判断文件是否为 PNG 格式
    参数:
        file_path (str): 文件路径
        strict_check (bool): 是否进行完整图像验证（需要PIL库）
    返回:
        bool: True 表示是有效 PNG，False 表示不是
    """
    real_path = server_utils.get_real_exist_file_path(file_png_path) # 校验文件存在并获取全路径

    # 方法1：完整图像验证（需要安装PIL/Pillow）
    if strict_check:
        try:
            with Image.open(real_path) as img:
                # 验证文件格式和完整性
                img.verify()  # 快速验证数据结构
                return img.format == 'PNG'
        except (IOError, SyntaxError):
            return False

    # 方法2：检查文件头标识（快速可靠）
    try:
        with open(real_path, 'rb') as f:
            # 读取前8字节（PNG文件头特征）
            header = f.read(8)
            # PNG标准文件头: 89 50 4E 47 0D 0A 1A 0A
            return header == b'\x89PNG\r\n\x1a\n'
    except IOError:
        return False

def is_ico(file_ico_path)->bool:
    real_path = server_utils.get_real_exist_file_path(file_ico_path) # 校验文件存在并获取全路径
    try:
        with open(real_path, 'rb') as f:
            # 读取前6字节（ICO文件头关键标识）
            header = f.read(6)
            if len(header) < 6:
                return False

            # 转换为十六进制便于检查
            hex_header = binascii.hexlify(header).decode('utf-8')

            # 验证文件头结构
            return (
                    hex_header.startswith('0000') and          # 前2字节必须为00 00
                    hex_header[4:8] in ('0100', '0200') and    # 图像类型为ICO或CUR
                    hex_header[8:12] != '0000'                 # 图像数量不能为0
            )
    except (IOError, OSError, binascii.Error):
      return False

# 设置gui界面图标: ico格式图标适合在windows系统中使用，png格式图标适合在其他系统中使用
def set_gui_icon(cur_gui:Union[tk.Tk,tk.Toplevel], local_img_path:str):
    logger.info(f"setting gui icon with path: {local_img_path}")
    real_path = server_utils.get_real_exist_file_path(local_img_path)
    logger.info(f"resolved icon path: {real_path}")

    system_platform = sys.platform
    logger.info(f"detected system platform: {system_platform}")

    try:
        # Windows专用处理
        if system_platform=="win32":
            logger.info("processing icon for windows platform")
            if is_ico(real_path):
                cur_gui.iconbitmap(real_path)
                logger.info("ico icon set successfully")
            else:
                logger.info("icon is not ico format, attempting conversion")
                # 尝试将PNG转为临时ICO
                ico_path = os.path.splitext(real_path)[0] + ".ico"
                logger.info(f"temporary ico path: {ico_path}")
                try:
                    img = Image.open(real_path)
                    logger.info("image opened successfully, converting to ico")
                    img.save(ico_path, format='ICO', sizes=[(256, 256)])
                    logger.info("image converted to ico successfully")
                    cur_gui.iconbitmap(ico_path)
                    logger.info("converted ico icon set successfully")
                    # 程序退出时清理临时文件
                    cur_gui.bind("<Destroy>", lambda event: os.remove(ico_path))
                    logger.info("cleanup handler registered for temporary ico file")
                except Exception: # noqa
                    logger.exception("windows icon conversion failed!")

        # 非Windows平台处理
        else:
            logger.info("processing icon for non-windows platform")
            # macOS/Linux优先使用PNG
            if is_png(real_path):
                logger.info("icon is png format, setting for non-windows platform")
                # 将图标缓存到全局变量防止被回收
                if not hasattr(cur_gui, '_tk_icons'):
                    cur_gui._tk_icons = []
                    logger.info("initialized icon cache for gui")
                icon = tk.PhotoImage(file=real_path)
                logger.info("photoimage created from png file")
                cur_gui._tk_icons.append(icon) # noqa
                cur_gui.wm_iconphoto(True, icon)
                logger.info("png icon set successfully")

                # macOS额外处理
                if system_platform == 'darwin':
                    logger.info("applying macos specific icon handling")
                    cur_gui.tk.call('wm', 'iconphoto', cur_gui._w, icon)  # noqa
                    logger.info("macos icon handling completed")
            else:
                logger.info("icon is not png format, attempting ico fallback")
                # 尝试非Windows平台使用ICO
                try:
                    cur_gui.iconbitmap(real_path)
                    logger.info("ico icon set successfully on non-windows platform")
                except:  # noqa
                    logger.warning("non-windows platform recommends png format icons")

    except Exception: # noqa
        logger.exception(f"icon setting failed: {local_img_path}!")

def common_gui_do(cur_gui:Union[tk.Tk,tk.Toplevel],local_icon_path:str):
    """每个gui窗口的通用设置: 窗口居中显示,设置图标"""
    server_utils.logger_print(msg=f"applying common gui settings to window: {cur_gui}", custom_logger=logger)
    server_utils.logger_print(msg="centering window", custom_logger=logger)
    center_window(cur_gui)
    server_utils.logger_print(msg="setting gui icon", custom_logger=logger)
    set_gui_icon(cur_gui, local_icon_path)
    server_utils.logger_print(msg="common gui settings applied successfully", custom_logger=logger)

def gui_exist(cur_gui:Optional[Union[tk.Tk,tk.Toplevel]])->bool:
    """判断窗口是否存在"""
    try:
        return cur_gui is not None and cur_gui.winfo_exists()
    except tk.TclError:
      return False

def gui_close(cur_gui:Optional[Union[tk.Tk,tk.Toplevel]]):
    """关闭窗口: 判断对应窗口是否存在,存在时关闭窗口"""
    if not  gui_exist(cur_gui):
        return
    try:
        if isinstance(cur_gui, tk.Tk):
            cur_gui.quit()
        cur_gui.destroy()
    except tk.TclError:
        # 窗口可能在这两步中已经被系统销毁了
        pass
