import argparse
import asyncio
import configparser
import copy
import functools
import logging
import multiprocessing
import sys
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict
from zoneinfo import ZoneInfo

from config import gui_constants, constants, gui_config_check
from models import webhook_server, server_data_manager
from utils import server_utils, gui_utils, self_log
from utils.gui_config_lock import MultiProcessUIConfigManager

logger: Optional[logging.Logger] = None


class WebhookServerGUI:
    """"
    gui主界面,传入的config_path为服务端配置文件路径,其必须有效
    """
    def __init__(self, server_config_path:str):
        server_utils.logger_print(msg="initializing webhook server gui", custom_logger=logger)
        # 配置管理器 参数值是gui界面生成配置文件的配置项的值,两者统一
        self.config_manager = MultiProcessUIConfigManager.get_singleton_instance(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging=gui_constants.ENABLE_SQL_LOGGING, zone=ZoneInfo(gui_constants.TIME_ZONE))
        # 服务端配置文件路径
        server_utils.logger_print(msg="setting up server config path", custom_logger=logger)
        self.check_server_config_path(server_config_path)
        self.server_config_path = server_config_path
        server_utils.logger_print(msg=f"using provided config path: {server_config_path}", custom_logger=logger)

        # 由于该值没有真正使用到，是使用在保存到配置文件和配置文件加载到该变量上，所以所有的变量的值都需要是字符串类型
        self.server_config_entries = {}
        # 该变量和client_tree一起使用
        self.client_info_entries = {}
        self.file_client_info_entries={}
        self.log_config_path:Optional[str] = None
        self.error_occured = False
        self.data_manager: Optional[server_data_manager.WebhookDataManager] = None
        self.no_data_show=True

        # gui元素变量
        self.server_config_dialog: Optional[tk.Toplevel] = None
        self.client_info_dialog: Optional[tk.Toplevel] = None
        self.about_dialog: Optional[tk.Toplevel] = None
        self.client_tree: Optional[ttk.Treeview] = None
        self.client_right_click_menu: Optional[tk.Menu] = None
        self.start_btn: Optional[ttk.Button] = None
        self.stop_btn: Optional[ttk.Button] = None
        self.user_server_config_entries: Dict[str, ttk.Entry] = {}  # 用户自定义配置项的Entry组件映射
        self.status_label: Optional[ttk.Label] = None
        self.real_time_data_table: Optional[ttk.Treeview] = None

        server_utils.logger_print(msg="creating main tkinter window", custom_logger=logger)
        self.root = tk.Tk()
        self.root.title("数据存储服务器")
        self.root.geometry("700x600")
        self.root.resizable(False, False)  # 设置主窗口大小固定不可调整
        server_utils.logger_print(msg="main window created with title, geometry and resizable settings", custom_logger=logger)
        # 在创建gui界面之后控制界面居中显示 --- 自定义图标
        server_utils.logger_print(msg="applying common gui settings", custom_logger=logger)
        gui_utils.common_gui_do(self.root,gui_constants.ICON_PATH)
        server_utils.logger_print(msg="common gui settings applied", custom_logger=logger)

        # 服务器进程
        self.server_process: Optional[multiprocessing.Process] = None
        server_utils.logger_print(msg="server process initialized as none", custom_logger=logger)

        # 加载配置
        server_utils.logger_print(msg="loading configuration", custom_logger=logger)
        self.load_config()
        server_utils.logger_print(msg="configuration loaded successfully", custom_logger=logger)

        # 创建界面
        server_utils.logger_print(msg="creating gui widgets", custom_logger=logger)
        self.create_widgets()
        server_utils.logger_print(msg="gui widgets created successfully", custom_logger=logger)

        # 监听关闭事件
        server_utils.logger_print(msg="setting up window close event handler", custom_logger=logger)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        server_utils.logger_print(msg="webhook server gui initialization completed", custom_logger=logger)

    def check_server_config_path(self,server_config_path:str):
        try:
            self.config_manager.main_gui_load_config(server_config_path)
        except Exception as e:
            messagebox.showerror(title="错误", message=str(e))
            raise

    def create_widgets(self):
        # 创建顶部菜单栏
        menu_bar = tk.Menu(self.root)
        self.root.config(menu=menu_bar)

        # 创建"设置"菜单
        settings_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="服务端配置", command=self.show_server_config_dialog)
        settings_menu.add_command(label="发信设备标识信息", command=self.show_client_info_dialog)
        settings_menu.add_command(label="关于", command=self.show_about_dialog)

        # 主内容区域（使用Frame作为容器）
        content_frame = ttk.Frame(self.root)
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 状态标签（直接显示，无框选）
        self.status_label = ttk.Label(content_frame, text="服务器未运行", font=("Arial", 12))
        self.status_label.pack(pady=(0, 10))

        # 创建数据表格区域（固定大小600x300）
        data_frame = ttk.LabelFrame(content_frame, text="实时数据")
        data_frame.pack(pady=(0, 10))
        data_frame.configure(width=600, height=270)
        data_frame.pack_propagate(False)  # 防止子组件改变父容器大小

        # 创建表格
        columns = ("message", "reception_time", "client_key", "is_read")
        self.real_time_data_table = ttk.Treeview(data_frame, columns=columns, show="headings", selectmode="browse")

        # 设置列属性
        self.real_time_data_table.column(columns[0], width=200, anchor="w", stretch=True)
        self.real_time_data_table.column(columns[1], width=150, anchor="center")
        self.real_time_data_table.column(columns[2], width=100, anchor="center")
        self.real_time_data_table.column(columns[3], width=120, anchor="center")

        # 设置表头
        self.real_time_data_table.heading(columns[0], text="数据内容")
        self.real_time_data_table.heading(columns[1], text="接收时间")
        self.real_time_data_table.heading(columns[2], text="设备标识")
        self.real_time_data_table.heading(columns[3], text="是否被其他设备读取")

        # 添加滚动条
        scrollbar = ttk.Scrollbar(data_frame, orient="vertical", command=self.real_time_data_table.yview) # noqa
        scrollbar.pack(side="right", fill="y")
        self.real_time_data_table.configure(yscrollcommand=scrollbar.set)

        # 始终显示表格（填充固定大小的容器）
        self.real_time_data_table.pack(fill='both', expand=True, padx=10, pady=10)

        # 绑定双击事件用于复制功能:实时数据表格中行的双击事件
        gui_utils.on_tree_double_click(self.real_time_data_table, self.on_real_time_data_table_double_click)
        self._no_data_show(show_msg="服务端没有启动不显示数据")

        # 按钮区域（固定在底部）
        btn_frame = ttk.Frame(self.root)
        btn_frame.pack(side=tk.BOTTOM, fill='x', padx=20, pady=20)
        self.start_btn = ttk.Button(btn_frame, text="启动服务器", command=self.start_server)
        self.start_btn.pack(side=tk.LEFT, padx=10)
        self.stop_btn = ttk.Button(btn_frame, text="停止服务器", command=self.stop_server, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=10)

    def _no_data_show(self,show_msg:str):
        """显示空数据提示信息"""
        self.real_time_data_table.delete(*self.real_time_data_table.get_children())
        # 如果没有数据，在表格中插入居中提示信息
        logger.info("no data available, showing centered empty message spanning all columns")
        self.real_time_data_table["displaycolumns"]="message"
        self.real_time_data_table.column("message", width=self.real_time_data_table.winfo_width(), anchor="center")
        self.real_time_data_table.insert("", "end", values=(show_msg,))
        self.no_data_show=True

    def refresh_data(self):
        """刷新表格数据"""
        logger.info("refreshing data in real-time data table")
        try:
            # 清除现有数据
            self.real_time_data_table.delete(*self.real_time_data_table.get_children())
            # 获取新数据
            logger.debug("fetching recent data from data manager")
            data = self.data_manager.get_recent_data(10)
            logger.debug(f"fetched {len(data)} data items")
            if not data:
                self._no_data_show(show_msg="当前没有数据发送到本服务端")
            else:
                self.no_data_show=False
                self.real_time_data_table["displaycolumns"]="#all"
                self.real_time_data_table.column("message", width=200, anchor="w", stretch=True)
                # 有数据时插入实际数据
                logger.debug("data available, inserting data into tree")
                # 插入新数据
                for i, item in enumerate(data):
                    # 是否被其他设备读取
                    message=item[0]
                    client_key=item[1]
                    reception_time=item[2]
                    is_read=item[3]
                    auth_status = "未被其他设备读取" if is_read == 0 else "已被其他设备读取数据"
                    logger.debug(f"inserting item {i+1}: message_length={len(message)}, client_key={client_key}, auth_status={auth_status}")

                    self.real_time_data_table.insert("", "end", values=(
                        message,
                        reception_time,
                        client_key,
                        auth_status
                    ))

            logger.info("data refresh completed successfully")
        except Exception:  # noqa
            logger.exception("error during data refresh")
            raise

    def on_real_time_data_table_double_click(self):
        """处理实时数据表格双击事件，实现数据内容复制功能"""
        if self.no_data_show:
            return
        logger.debug("webhook real-time data table double click event triggered")
        # 获取双击位置
        selected=self.real_time_data_table.selection()
        if not selected or len(selected) > 1 or not self.real_time_data_table.exists(selected[0]):
            return
        try:
            item_id = selected[0]
            # 选择该行
            self.real_time_data_table.selection_set(item_id)
            # 获取选中行的数据
            values = self.real_time_data_table.item(item_id, 'values')
            if values and len(values) > 0:
                # 获取数据内容列（第一列：message）
                message_content = values[0]
                # 复制到剪贴板
                self.copy_to_clipboard(message_content)
                logger.debug(f"copied message content to clipboard via double click, content length: {len(message_content)}")
                # 显示复制成功提示窗口
                self.show_copy_success_popup(message_content)
        except Exception:  # noqa
            logger.exception("error during webhook real-time data table double click handling")

    def copy_to_clipboard(self, text):
        """将文本复制到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.root.update()  # 确保剪贴板更新
            logger.debug("webhook data message copied to clipboard successfully!")
        except Exception:  # noqa
            logger.exception("error during copying webhook data message to clipboard")

    def show_copy_success_popup(self, message_content):
        """显示复制成功的提示窗口，1秒后自动消失"""
        try:
            logger.debug("showing webhook data message copy success popup")
            # 创建提示窗口
            popup = tk.Toplevel(self.root)
            popup.title("复制成功")
            popup.geometry("300x100")
            popup.resizable(False, False)
            popup.transient(self.root)
            popup.grab_set()
            # 设置窗口居中
            popup.update_idletasks()
            x = (popup.winfo_screenwidth() // 2) - (popup.winfo_width() // 2)
            y = (popup.winfo_screenheight() // 2) - (popup.winfo_height() // 2)
            popup.geometry(f"+{x}+{y}")

            # 创建提示内容
            frame = ttk.Frame(popup)
            frame.pack(fill='both', expand=True, padx=20, pady=20)

            # 显示复制成功信息
            success_label = ttk.Label(frame, text="数据内容复制成功！", font=("Arial", 12, "bold"))
            success_label.pack(pady=(0, 10))

            # 显示复制的内容（截取前50个字符）
            content_preview = message_content[:50] + "..." if len(message_content) > 50 else message_content
            content_label = ttk.Label(frame, text=f"已复制：{content_preview}", font=("Arial", 9))
            content_label.pack()

            # 1秒后自动关闭窗口
            popup.after(1000, popup.destroy) # noqa
            logger.debug("copy success popup displayed, will auto-close in 1 second")
        except Exception:  # noqa
            logger.exception("error during showing copy success popup")

    def show_server_config_dialog(self):
        if self.server_config_dialog and self.server_config_dialog.winfo_exists():
            self.server_config_dialog.lift()
            return

        self.server_config_dialog = tk.Toplevel(self.root)
        self.server_config_dialog.title("服务端配置")
        self.server_config_dialog.geometry("500x400")
        self.server_config_dialog.transient(self.root)
        self.server_config_dialog.grab_set()
        # 在创建gui界面之后控制界面居中显示 --- 自定义图标
        gui_utils.common_gui_do(self.server_config_dialog,gui_constants.ICON_PATH)
        # 服务端配置区域
        config_frame = ttk.LabelFrame(self.server_config_dialog, text="服务端配置")
        config_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 清空之前的用户配置Entry映射
        self.user_server_config_entries.clear()

        # 动态生成USER_CUSTOM_KEYS中的Entry组件
        current_row = 0
        for config_key in gui_constants.USER_CUSTOM_KEYS:
            # 创建标签，将配置键转换为更友好的显示名称
            display_label = constants.SERVER_KEY_DESC[config_key]
            label_widget = ttk.Label(config_frame, text=f"{display_label}:")
            label_widget.grid(row=current_row, column=0, sticky='e', padx=5, pady=5)

            # 创建Entry组件
            entry_widget = ttk.Entry(config_frame, width=50)
            entry_widget.grid(row=current_row, column=1, sticky='w', padx=5, pady=5)

            # 如果配置中已有值则显示
            existing_value = self.server_config_entries.get(config_key)
            if existing_value:
                entry_widget.insert(0, existing_value)

            # 保存Entry组件引用到映射中
            self.user_server_config_entries[config_key] = entry_widget
            current_row += 1

        # 添加保存按钮到服务端配置区域
        save_frame = ttk.Frame(config_frame)
        save_frame.grid(row=current_row, column=0, columnspan=2, pady=20)

        ttk.Button(save_frame, text="保存服务端配置", command=self.save_server_config).pack(side=tk.LEFT, padx=10)

        # 绑定关闭事件
        self.server_config_dialog.protocol("WM_DELETE_WINDOW", self.server_config_dialog.destroy)

    def show_about_dialog(self):
        if self.about_dialog and self.about_dialog.winfo_exists():
            self.about_dialog.lift()
            return
        cur_gui_width = 550
        mult_content_line_additional_width = 150
        self.about_dialog = tk.Toplevel(self.root)
        self.about_dialog.title("关于")
        self.about_dialog.geometry("400x200")
        self.about_dialog.resizable(False, False)
        self.about_dialog.minsize(cur_gui_width, 250)
        self.about_dialog.transient(self.root)
        self.about_dialog.grab_set()
        # 在创建gui界面之后控制界面居中显示 --- 自定义图标
        gui_utils.common_gui_do(self.about_dialog,gui_constants.ICON_PATH)
        # 主框架
        main_frame = ttk.Frame(self.about_dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 标题
        title_label = ttk.Label(main_frame,
                                text=gui_constants.SOFTWARE_NAME,
                                font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 使用网格布局创建整齐的键值对
        labels = [
            ("版本", gui_constants.VERSION),
            ("联系方式", gui_constants.CONTACT_INFORMATION),
            ("发布日期", gui_constants.RELEASE_TIME)
        ]

        # 创建标签和内容
        for row, (label_text, value_text) in enumerate(labels, start=1):
            # 标签列 (左对齐)
            ttk.Label(main_frame, text=label_text + "：",
                      justify=tk.RIGHT, anchor="e").grid(
                row=row, column=0, sticky="e", padx=(0, 5), pady=2)

            # 值列 (左对齐)
            ttk.Label(main_frame, text=value_text,
                      anchor="w").grid(
                row=row, column=1, sticky="w", pady=2)

        # 简介 (单独一行)
        row = len(labels) + 1
        ttk.Label(main_frame, text="简要介绍：",
                  anchor="e").grid(row=row, column=0, sticky="e", padx=(0, 5), pady=(10, 0))

        # 多行简介 (自动换行)
        ttk.Label(main_frame,text=gui_constants.INSTRUCTION_SOFTWARE_USE,wraplength=cur_gui_width-mult_content_line_additional_width,justify=tk.LEFT).grid(row=row, column=1, sticky="w", pady=(10, 0))

        # 许可协议 (单独一行)
        row += 1
        ttk.Label(main_frame, text="许可协议：",
                  anchor="e").grid(row=row, column=0, sticky="e", padx=(0, 5), pady=(10, 0))
        # 许可协议内容 自动换行
        license_agreement_content = gui_constants.LICENSE_AGREEMENT.replace(" ", "\u00A0")
        ttk.Label(main_frame,text=license_agreement_content,wraplength=cur_gui_width-mult_content_line_additional_width,justify=tk.LEFT).grid(row=row, column=1, sticky="w", pady=(10, 0))

        # 配置列权重
        main_frame.columnconfigure(0, weight=1, minsize=80)  # 标签列最小宽度
        main_frame.columnconfigure(1, weight=3)  # 内容列占3/4宽度

        # 绑定关闭事件
        self.about_dialog.protocol("WM_DELETE_WINDOW", self.about_dialog.destroy)

    def show_client_info_dialog(self):
        if self.client_info_dialog and self.client_info_dialog.winfo_exists():
            self.client_info_dialog.lift()
            return

        self.client_info_dialog = tk.Toplevel(self.root)
        self.client_info_dialog.title("发信设备标识管理")
        self.client_info_dialog.geometry("700x500")
        self.client_info_dialog.transient(self.root)
        self.client_info_dialog.grab_set()
        # 在创建gui界面之后控制界面居中显示 --- 自定义图标
        gui_utils.common_gui_do(self.client_info_dialog,gui_constants.ICON_PATH)
        # 发信设备标识信息区域
        client_frame = ttk.LabelFrame(self.client_info_dialog, text="发信设备标识信息")
        client_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 发信设备标识信息表格
        tree_frame = ttk.Frame(client_frame)
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        scrollbar = ttk.Scrollbar(tree_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.client_tree = ttk.Treeview(tree_frame, selectmode="extended",
                                        columns=("key", "description"), show='headings',
                                        yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.client_tree.yview)

        self.client_tree.heading("key", text="发信设备标识")
        self.client_tree.heading("description", text="发信设备描述信息")
        self.client_tree.column("key", width=200)
        self.client_tree.column("description", width=400)
        self.client_tree.pack(fill='both', expand=True)

        # 添加双击事件绑定
        gui_utils.on_tree_double_click(self.client_tree, self.__edit_client)

        # 新增右键菜单功能
        self.client_right_click_menu = tk.Menu(self.client_tree, tearoff=0)
        self.client_right_click_menu.add_command(label="编辑", command=self.__edit_client)
        # 绑定右键点击事件
        gui_utils.right_click_show_menu(self.client_tree, self.client_right_click_menu)

        if self.client_info_entries:
            for client_key, client_desc in self.client_info_entries.items():
                self.client_tree.insert("", tk.END, values=(client_key, client_desc))

        # 操作按钮区域
        btn_frame = ttk.Frame(client_frame)
        btn_frame.pack(pady=10)

        ttk.Button(btn_frame, text="添加", command=self.__add_client).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="编辑", command=self.__edit_client).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除", command=self.__delete_client).pack(side=tk.LEFT, padx=5)

        # 添加保存按钮到发信设备标识信息区域
        save_frame = ttk.Frame(client_frame)
        save_frame.pack(pady=10)

        ttk.Button(save_frame, text="保存发信设备信息", command=self.save_client_config).pack(side=tk.LEFT, padx=10)
        def on_close():
            # 检查是否有未保存的修改
            if self.file_client_info_entries != self.client_info_entries:
                if messagebox.askyesno("未保存的修改","有未保存的修改，是否放弃更改?"):
                    # 放弃更改并关闭窗口
                    self.client_info_entries=copy.deepcopy(self.file_client_info_entries)
                    gui_utils.gui_close(self.client_info_dialog)
                else:
                    # 用户选择继续编辑，不关闭窗口
                    return
            else:
                gui_utils.gui_close(self.client_info_dialog)

        # 绑定关闭事件
        self.client_info_dialog.protocol("WM_DELETE_WINDOW", on_close)


    # 保存或者修改:[当cur_client_id值存在时,即修改] --- client_key不能重复
    def __save_client_info(self, dialog: tk.Toplevel, key_entry: ttk.Entry, desc_entry: ttk.Entry, cur_selected_id: str = None):
        # 默认选择节点id存在即client_tree中存在该节点
        client_key = key_entry.get()
        client_desc = desc_entry.get()
        if not client_key or not client_desc:
            messagebox.showerror("错误", "发信设备标识和描述不能为空")
            return
        if not server_utils.check_one_client_info(client_key, client_desc):
            messagebox.showerror("错误", constants.ERROR_CLIENT_INPUT_MSG)
            return
        # 新的设备标识不能与已有的重复
        old_client_key = None
        if cur_selected_id:
            old_client_key = self.client_tree.item(cur_selected_id, 'values')[0]
        if client_key in self.client_info_entries and (cur_selected_id is None or old_client_key != client_key):
            messagebox.showerror("错误", "发信设备标识不能和已有的设备标识重复,请重新输入重试")
            return
        if cur_selected_id:
            # 修改发信设备信息
            self.client_tree.item(cur_selected_id, values=(client_key, client_desc))
            del self.client_info_entries[old_client_key]
        else:
            # 添加发信设备信息
            self.client_tree.insert("", tk.END, values=(client_key, client_desc))
        self.client_info_entries[client_key] = client_desc
        dialog.destroy()

    def __show_add_client_dialog(self, dialog_title: str, cur_selected_id: str = None):
        # 显示添加发信设备标识对话框
        cur_key = ""
        cur_desc = ""
        if cur_selected_id:
            cur_key, cur_desc = self.client_tree.item(cur_selected_id, 'values')
        dialog = tk.Toplevel(self.root)
        dialog.title(dialog_title)
        dialog.geometry("400x200")  # 设置窗口大小
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        # 在创建gui界面之后控制界面居中显示 --- 自定义图标
        gui_utils.common_gui_do(dialog,gui_constants.ICON_PATH)
        ttk.Label(dialog, text="发信设备标识:").grid(row=0, column=0, padx=10, pady=5, sticky="e")
        key_entry = ttk.Entry(dialog, width=40)
        key_entry.insert(0, cur_key)
        key_entry.grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="发信设备描述:").grid(row=1, column=0, padx=10, pady=5, sticky="e")
        desc_entry = ttk.Entry(dialog, width=40)
        desc_entry.insert(0, cur_desc)
        desc_entry.grid(row=1, column=1, padx=10, pady=5)

        ttk.Button(dialog, text="保存",
                   command=functools.partial(self.__save_client_info, dialog, key_entry, desc_entry, cur_selected_id)).grid(
            row=2, column=0, columnspan=2, padx=10, pady=15)

        # 绑定关闭事件
        dialog.protocol("WM_DELETE_WINDOW", dialog.destroy)

    def __add_client(self):
        # 添加发信设备标识对话框
        self.__show_add_client_dialog("添加发信设备信息")

    def __edit_client(self):
        selected = self.client_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个发信设备信息进行编辑")
            return
        # 只能单选
        if len(selected) > 1:
            messagebox.showwarning("警告", "只能选择一个发信设备信息进行编辑")
            return
        self.__show_add_client_dialog("编辑发信设备信息", selected[0])

    def __delete_client(self):
        # 选中节点id[可多选]
        selected = self.client_tree.selection()
        if selected:
            for item_id in selected:
                del self.client_info_entries[self.client_tree.item(item_id, 'values')[0]]
                self.client_tree.delete(item_id)

    def _handle_user_operation_error(self, error, context,no_exit=True):
        """集中处理用户操作错误"""
        self.error_occured = True
        gui_utils.handle_code_error(error, context,no_exit=no_exit)


    def load_config(self):
        try:
            server_config = configparser.ConfigParser(interpolation=None)
            server_config.read(self.server_config_path, encoding="utf-8")
            # 加载服务端配置:其中存在用户界面自定义配置项，这时对应的配置项就不需要检验
            self.server_config_entries =server_utils.section_to_dict(server_config, "server", True)
            # 加载发信设备标识信息
            if "client_info" in server_config:
                self.client_info_entries = server_utils.section_to_dict(server_config, "client_info",allow_empty_section=True)
                self.file_client_info_entries=copy.deepcopy(self.client_info_entries)
        except Exception as server_config_ex:
            self._handle_user_operation_error(server_config_ex, "加载服务端配置失败",no_exit=False)

        # 用户界面自定义配置项缺失，提示用户必须填写所有必填字段
        miss_user_custom_keys = set(gui_constants.USER_CUSTOM_KEYS) - set(self.server_config_entries.keys())
        if not self.client_info_entries:
            miss_user_custom_keys.add("client_info")
        if miss_user_custom_keys:
            key_desc_set = {constants.SERVER_KEY_DESC[user_custom_key] for user_custom_key in miss_user_custom_keys}
            msg=f"服务端配置项缺失: 【{', '.join(key_desc_set)}】, 请填写之后保存配置然后运行服务端!"
            messagebox.showinfo("提示", msg)

        self.log_config_path = self.server_config_entries["log_config_path"]
        global logger
        self_log.setup_logging(self.log_config_path,time_zone=ZoneInfo(self.server_config_entries["time_zone"]))
        logger = logging.getLogger(__name__)

    def check_all_config__before_start(self):
        # 在服务端正式启动前,进行配置项的校验 --- 不需要进行保存
        if self.server_process and self.server_process.is_alive():
            self._handle_user_operation_error(None, "服务器正在运行,请先停止服务器,再保存配置!")
            return
        self.error_occured=False
        # 用户自定义配置项不能为空
        try:
            gui_config_check.check_server_config_file_to_runtime_webhook(self.server_config_path)
        except Exception as check_config_file_except:
            self._handle_user_operation_error(check_config_file_except, str(check_config_file_except))
        # 阻断后续执行
        if self.error_occured:
            return

    def save_server_config(self):
        if self.server_process and self.server_process.is_alive():
            self._handle_user_operation_error(None, "服务器正在运行,请先停止服务器,再保存服务端配置项!")
            return
        cur_server_config_entries = {}
        for config_key, entry_widget in self.user_server_config_entries.items():
            cur_server_config_entries[config_key] = str(entry_widget.get())
        try:
            self.config_manager.update_config('server', cur_server_config_entries)
            self.server_config_entries.update(cur_server_config_entries)
        except Exception as check_server_config_except:
            self._handle_user_operation_error(check_server_config_except, f"保存服务端配置失败\n {check_server_config_except}")
            return

        messagebox.showinfo("成功", "服务端配置项保存成功!")

    def save_client_config(self):
        if self.server_process and self.server_process.is_alive():
            self._handle_user_operation_error(None, "服务器正在运行,请先停止服务器,再保存发信设备标识信息!")
            return
        try:
           self.config_manager.update_config("client_info", self.client_info_entries)
           self.file_client_info_entries=copy.deepcopy(self.client_info_entries)
        except Exception as check_client_config_except:
           self._handle_user_operation_error(check_client_config_except, str(check_client_config_except))
           return
        messagebox.showinfo("成功", "发信设备标识信息保存成功!")
    def start_server(self):
        logger.info("starting server process")

        self.error_occured=False
        self.check_all_config__before_start()  # 确保保存最新配置
        if self.error_occured:
            logger.error("configuration check failed, aborting server start")
            return

        try:
            logger.info("creating server process with config path: %s", self.server_config_path)

            # 启动服务器进程
            self.server_process = multiprocessing.Process(target=WebhookServerGUI.run_server, args=(self.server_config_path,), daemon=True)
            logger.info("starting server process")
            self.server_process.start()
            logger.info("server process started with pid: %s", self.server_process.pid)

            if not self.server_process.is_alive():
                raise ValueError("启动服务器进程失败!")

            logger.info("server process is alive, updating ui state")
            self._update_start_btn_state(False)
            self.status_label.config(text="服务器运行中",foreground="green")
            self._no_data_show(show_msg="服务端正在启动,请稍候...")
            logger.info("initializing data manager")
            self.data_manager = server_data_manager.WebhookDataManager(self.server_config_entries["message_data_path"], ZoneInfo(self.server_config_entries["time_zone"]), enable_sql_logging=self.server_config_entries["enable_sql_logging"])
            logger.info("scheduling server status check in 1 second")
            self.root.after(6000, self.check_server_status)# type: ignore
            logger.info("server start process completed successfully")
        except Exception as start_server_exp:
            logger.exception("server start process failed!", exc_info=True)
            self._handle_user_operation_error(start_server_exp, "启动服务器失败")
            self.server_process = None
            self._update_start_btn_state(True)
            self.status_label.config(text="服务器启动失败",foreground="red")


    def check_server_status(self)->None:
        # 检查服务器状态
        logger.debug("checking server status")
        try:
            server_process = getattr(self, 'server_process', None)
            if server_process is None:
                logger.warning("no server process found, status check skipped")
                return
            logger.debug("checking if server process (pid: %s) is alive", server_process.pid)
            if not server_process.is_alive():
                exit_code= server_process.exitcode
                logger.warning(f"server process (pid: {server_process.pid}) exited with code: {exit_code}")
                if exit_code !=0:
                    error_msg:str =f"服务器进程异常退出,退出码: ({exit_code})"
                    logger.error(error_msg)
                    self._handle_user_operation_error(error=None, context=error_msg)
                    self.status_label.config(text="服务器异常停止",foreground="red")
                else:
                    logger.info("server process exited normally")
                    self.status_label.config(text="服务器已停止",foreground="black")

                logger.info(f"server process exited with code: {exit_code}")

                self.server_process = None
                self._update_start_btn_state(True)
            else:
                logger.debug("server process is still alive, scheduling next status check in 5 seconds")
                self.status_label.config(text="服务器运行中",foreground="green")
                # 定时检测服务器状态
                if gui_utils.gui_exist(self.root):
                    logger.debug("refreshing data and scheduling next status check")
                    self.refresh_data()
                    self.root.after(5000, self.check_server_status)# type: ignore
                else:
                    logger.warning("root window not available, stopping status checks")

        except BaseException: # noqa
            logger.exception("error during server status check!", exc_info=True)
            self.stop_server()
            self.server_process = None
            self._update_start_btn_state(True)
            self.status_label.config(text="服务器异常停止",foreground="red")

    def _update_start_btn_state(self,active:bool) -> None:
        """
        更新启动/停止按钮状态
        Args: active: 是否激活启动按钮 (True=启动可用, False=停止可用)
        """
        if hasattr(self,'start_btn') and hasattr(self,'stop_btn'):
            self.start_btn.config(state=tk.NORMAL if active else tk.DISABLED)
            self.stop_btn.config(state=tk.DISABLED if active else tk.NORMAL)

    @staticmethod
    def run_server(server_config_path:str):
        """必须是静态函数,不能涉及到self实例变量"""
        server_utils.logger_print(msg=f"run_server called with config_path: {server_config_path}", custom_logger=logger)
        try:
            server_utils.logger_print(msg="starting async server execution", custom_logger=logger)
            asyncio.run(webhook_server.run_server_with_path(server_config_path))
            server_utils.logger_print(msg="async server execution completed", custom_logger=logger)
        except Exception as run_server_exp:
            server_utils.logger_print(msg="server execution failed!", custom_logger=logger, use_exception=True, exception=run_server_exp, print_error=True)
            sys.exit(1)

    def stop_server(self):
        server_utils.logger_print(msg="stopping server process", custom_logger=logger)

        if hasattr(self, 'server_process') and self.server_process and self.server_process.is_alive():
            server_utils.logger_print(msg=f"terminating server process (pid: {self.server_process.pid})", custom_logger=logger)
            # 终止服务器进程
            self.server_process.terminate()
            server_utils.logger_print(msg="waiting for server process to terminate (timeout: 5 seconds)", custom_logger=logger)
            self.server_process.join(timeout=5)
            if self.server_process.is_alive():
                server_utils.logger_print(msg="server process did not terminate gracefully, forcing kill", custom_logger=logger, log_level=logging.WARNING)
                # 强制终止服务器进程
                self.server_process.kill()
                self.server_process.join()  # 确保进程终止
            else:
                server_utils.logger_print(msg="server process terminated successfully", custom_logger=logger)
            self.status_label.config(text="服务器已停止",foreground="black")
        else:
            server_utils.logger_print(msg="no running server process found", custom_logger=logger)

        server_utils.logger_print(msg="updating ui state after server stop", custom_logger=logger)
        self.server_process = None

        server_utils.logger_print(msg="updating ui state after server stop", custom_logger=logger)
        if hasattr(self, 'start_btn') and hasattr(self, 'stop_btn'):
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)

        # 清除数据显示
        server_utils.logger_print(msg="clearing data display after server stop", custom_logger=logger)
        # 清除现有数据（包括空数据提示）
        if hasattr(self, 'real_time_data_table'):
            self.real_time_data_table.delete(*self.real_time_data_table.get_children())
            self._no_data_show(show_msg="服务端停止不显示数据")
        server_utils.logger_print(msg="server stop process completed", custom_logger=logger)

    def on_closing(self):
        # 考虑到配置在加载时出现异常和配置时出现异常的情况,这个时候保存配置就会使得原本在配置文件中的配置丢失,所以这里不保存配置
        # 关闭前检查服务器是否运行
        if hasattr(self, 'server_process') and self.server_process and self.server_process.is_alive():
            if messagebox.askokcancel("退出", "服务器正在运行，确定要退出吗？"):
                self.stop_server()
            else:
                # 取消退出
                return
        gui_utils.gui_close(self.root)
        self.root=None


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    config_path = parser.parse_args().config
    main_app = WebhookServerGUI(config_path)
    main_app.root.mainloop()
