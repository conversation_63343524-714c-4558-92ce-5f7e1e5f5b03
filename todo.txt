铁路沿线  https://www.aliyundrive.com/s/riWx5h9X3vJ
激流中国 

将所有需要的学习资料复制到硬盘柜中 --- 本地的，U盘内的，云盘内的
所有待做事项迁移到本地

只需要在gui界面配置默认的配置项的值，其他地方是不需要有默认值的，是从配置文件中读取的

对应的新增修改删除操作可能失败，如何实现事务的一致性 BaseConfigUniquenessManager

不同的配置文件路径只能在新建配置时确定,其他任何情况下都不能被修改
配置记录表中新增字段，对应配置是否有效【默认是有效的】 失效的原因 --- 心跳只删除配置项记录，只有在配置加载界面中的清除失效按钮才能删除失效的记录
心跳只能在没有被占用的配置记录中进行有效性判断

程序多开的处理情况


加载配置界面前，执行一次进程间配置有效性检测函数避免误认为无效的进程占用了空置的配置文件
直接在ConfigUniquenessManager类的基础上修改原有类和新增类实现：编辑配置时多进程唯一和运行时多进程配置唯一
编辑配置项时，服务端配置文件路径是不可以修改的，而且修改成功sql中的记录之后需要保存到配置文件中，一次可以修改一个或者多个
所有有效的配置文件都加载到该数据库中，不论其是否被进程使用
程序打开时，进行配置加载界面

MultiProcessUIConfigManager提取公共方法：
补充logger
