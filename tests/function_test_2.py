import asyncio
import logging
import os
import socket
import sys
import time

import pytest

from models import webhook_server
from utils import server_utils

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

logger = logging.getLogger(__name__)

# -------------------- 本地端口可用性测试 ------------------------
@pytest.fixture(scope="function")
def free_port():
    """动态获取可用端口的fixture"""
    with socket.socket() as s:
        s.bind(("127.0.0.1", 0))
        yield s.getsockname()[1]

@pytest.fixture(scope="function")
def used_port():
    """生成被占用端口的fixture"""
    s = socket.socket()
    s.bind(("127.0.0.1", 0))
    port = s.getsockname()[1]
    yield port
    s.close()  # 自动释放端口

# 参数化测试非法端口场景

# -------------------- host:端口可连接性测试 ------------------------

@pytest.fixture(scope="module")
def server_demo():
    """启动临时测试服务器（模块级）"""
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server.bind(("0.0.0.0", 0))
    print(f"Server bound to: {server.getsockname()}")
    server.listen(1)
    # 添加连接接受处理
    import threading
    def accept_connections(s):
        while True:
            conn, addr = s.accept()
            conn.close()

    # 启动后台线程处理连接
    t = threading.Thread(target=accept_connections, args=(server,), daemon=True)
    t.start()
    yield server
    server.close()

@pytest.mark.parametrize("host,expected", [
    ("localhost", True),
    ("************", True),
    ("127.0.0.1", True),
    ("************", False)
], ids=["localhost", "lan_host", "loopback_address","invalid_address"])

def test_remote_connection(host, server_demo, expected):
    """测试远程主机连接性"""
    _, port = server_demo.getsockname()
    print(port)
    assert server_utils.host_port_connect(host, port) == expected

def test_webhook_server_connection():
    """验证超时处理机制"""
    if not server_utils.host_port_connect("127.0.0.1", 8000, timeout=0.1) :
        print("Webhook server not running, skipping test")
        return
    else:
        print("Webhook server running, testing connection success")

def run_many_server():
    config_path = "D:/Git/python-samples-hub/resources/server_config.ini"
    asyncio.run(webhook_server.run_server_with_path(config_path))
    print("Server started")
    asyncio.run(webhook_server.run_server_with_path(config_path))

def test_run_many_server_test():
    run_many_server()
    time.sleep(10)

